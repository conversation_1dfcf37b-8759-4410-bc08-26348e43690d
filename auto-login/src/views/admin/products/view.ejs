<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin - View Product</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Product Details</h1>
        <div class="space-x-2">
          <button id="editBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Edit Product
          </button>
          <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Products
          </button>
        </div>
      </div>

      <div class="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div class="p-6">
          <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>

          <!-- Product Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Product Image -->
            <div id="image-preview-container" class="mb-6">
              <h2 class="text-xl font-semibold mb-4">Product Image</h2>
              <div class="border rounded-lg overflow-hidden h-64 bg-gray-100 flex items-center justify-center">
                <img id="product-image" src="" alt="Product Image" class="w-full h-full object-contain" />
              </div>
            </div>

            <div>
              <h2 class="text-xl font-semibold mb-4">Basic Information</h2>

              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium text-gray-500">ID</p>
                  <p id="product-id" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Name</p>
                  <p id="product-name" class="mt-1 text-sm text-gray-900"></p>
                </div>

                <div>
                  <p class="text-sm font-medium text-gray-500">Categories</p>
                  <div id="product-categories" class="mt-1 flex flex-wrap gap-1"></div>
                </div>

  
              </div>
            </div>
          </div>

          <!-- Product Description -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Description</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p id="product-description" class="text-sm text-gray-700 whitespace-pre-line"></p>
            </div>
          </div>

          <!-- Product Features -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Features</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <ul id="product-features" class="list-disc pl-5 text-sm text-gray-700 space-y-1">
                <!-- Features will be populated here -->
              </ul>
            </div>
          </div>

          <!-- Prompt Features -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Prompt Features</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center">
                  <span class="text-sm font-medium text-gray-500 mr-2">Has Prompt Library:</span>
                  <span id="has-prompt-library" class="text-sm font-semibold"></span>
                </div>
                <div class="flex items-center">
                  <span class="text-sm font-medium text-gray-500 mr-2">Has Prompt Video:</span>
                  <span id="has-prompt-video" class="text-sm font-semibold"></span>
                </div>
              </div>
            </div>
          </div>

          <!-- Product Durations -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Available Durations</h2>
            <div id="product-durations" class="space-y-2">
              <!-- Durations will be populated here -->
            </div>
          </div>

          <!-- Product Accounts -->
          <div class="mt-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold">Assigned Accounts</h2>
              <button id="assignAccountBtn" class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-2 px-4 rounded flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Assign Account</span>
              </button>
            </div>
            <div id="accounts-list" class="space-y-2">
              <!-- Accounts will be populated here -->
            </div>
          </div>



          <!-- Created/Updated Info -->
          <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p class="text-sm font-medium text-gray-500">Created At</p>
              <p id="product-created" class="mt-1 text-sm text-gray-900"></p>
            </div>

            <div>
              <p class="text-sm font-medium text-gray-500">Updated At</p>
              <p id="product-updated" class="mt-1 text-sm text-gray-900"></p>
            </div>
          </div>

          <!-- Actions -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            <div class="flex space-x-2">
              <button id="deleteBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete Product
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Confirm Delete</h3>
        <p class="mb-6">Are you sure you want to delete this product? This action cannot be undone.</p>
        <div class="flex justify-end space-x-2">
          <button id="cancelDeleteBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
          </button>
          <button id="confirmDeleteBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
          </button>
        </div>
      </div>
    </div>

    <!-- Assign Account Modal -->
    <div id="assignAccountModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto w-full">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">Assign Account</h3>
          <button id="closeAssignModal" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form id="assignAccountForm">
          <div class="mb-4">
            <label for="accountSelect" class="block text-sm font-medium text-gray-700 mb-2">Select Account</label>
            <select id="accountSelect" name="accountId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              <option value="">Select an account</option>
              <!-- Accounts will be populated here -->
            </select>
          </div>
          <div class="flex justify-end space-x-2">
            <button type="button" id="cancelAssignBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              Cancel
            </button>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Assign
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Remove Account Modal -->
    <div id="removeAccountModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-semibold mb-4">Confirm Remove</h3>
        <p class="mb-6">Are you sure you want to remove this account from the product?</p>
        <input type="hidden" id="removeAccountId">
        <div class="flex justify-end space-x-2">
          <button id="cancelRemoveAccountBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
          </button>
          <button id="confirmRemoveAccountBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Remove
          </button>
        </div>
      </div>
    </div>

    <script>
      // Get product ID from server-rendered data
      const productId = `<%- id %>`; // Use the ID passed from the server
      
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show toast notification
      function showToast(message, type = 'success') {
        const backgroundColor = type === 'success' ? '#48bb78' : '#f56565';

        Toastify({
          text: message,
          duration: 3000,
          close: true,
          gravity: "top",
          position: "right",
          backgroundColor,
          stopOnFocus: true
        }).showToast();
      }

      // Show error message
      function showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        showToast(message, 'error');
      }

      // Format date
      function formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
      }

      // Format price
      function formatPrice(price) {
        if (!price) return '$0.00';
        return '$' + Number(price).toFixed(2);
      }

      // Fetch product details
      async function fetchProduct(id) {
        if (!setupAxios()) return;

        try {
          const response = await axios.get(`/products/${id}`);
          return response.data;
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to fetch product details. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Fetch product accounts
      async function fetchProductAccounts(id) {
        if (!setupAxios()) return [];

        try {
          const response = await axios.get(`/products/${id}/accounts`);
          return response.data;
        } catch (error) {
          console.error('Error fetching product accounts:', error);
          return [];
        }
      }

      // Fetch all accounts
      async function fetchAllAccounts() {
        if (!setupAxios()) return [];

        try {
          const response = await axios.get('/accounts?limit=100');
          return response.data.data || [];
        } catch (error) {
          console.error('Error fetching accounts:', error);
          return [];
        }
      }
      
      // Get accounts that can be assigned to the product
      async function getAssignableAccounts(productId) {
        const [allAccounts, assignedAccounts] = await Promise.all([
          fetchAllAccounts(),
          fetchProductAccounts(productId)
        ]);
        
        // Get IDs of accounts already assigned to this product
        const assignedAccountIds = assignedAccounts.map(ap => ap.account.id);
        
        // Filter out accounts that are already assigned to this product
        return allAccounts.filter(account => !assignedAccountIds.includes(account.id));
      }

      // Display product details
      async function displayProductDetails(product) {
        document.getElementById('product-id').textContent = product.id;
        document.getElementById('product-name').textContent = product.name || 'N/A';
        
        // Display all categories
        const categoriesContainer = document.getElementById('product-categories');
        categoriesContainer.innerHTML = '';
        
        // Add primary category if exists
        if (product.category) {
          const categoryBadge = document.createElement('span');
          categoryBadge.className = 'inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-1 mb-1';
          categoryBadge.textContent = product.category.name;
          categoriesContainer.appendChild(categoryBadge);
        }
        
        // Add additional categories if they exist
        if (product.categories && product.categories.length > 0) {
          product.categories.forEach(category => {
            const categoryBadge = document.createElement('span');
            categoryBadge.className = 'inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-1 mb-1';
            categoryBadge.textContent = category.name;
            categoriesContainer.appendChild(categoryBadge);
          });
        }
        
        // If no categories at all, show a message
        if ((!product.category) && (!product.categories || product.categories.length === 0)) {
          categoriesContainer.textContent = 'No categories';
        }
        
        document.getElementById('product-description').textContent = product.description || 'No description available';
        document.getElementById('product-created').textContent = formatDate(product.created_at);
        document.getElementById('product-updated').textContent = formatDate(product.updated_at);
        
        // Display prompt features
        document.getElementById('has-prompt-library').textContent = product.has_prompt_library ? 'Yes' : 'No';
        document.getElementById('has-prompt-library').className = `text-sm font-semibold ${product.has_prompt_library ? 'text-green-600' : 'text-red-600'}`;
        
        document.getElementById('has-prompt-video').textContent = product.has_prompt_video ? 'Yes' : 'No';
        document.getElementById('has-prompt-video').className = `text-sm font-semibold ${product.has_prompt_video ? 'text-green-600' : 'text-red-600'}`;

        // Display image if available
        if (product.image_url) {
          document.getElementById('product-image').src = product.image_url;
        } else {
          document.getElementById('product-image').src = 'https://via.placeholder.com/400x400?text=No+Image';
        }
        
        // Display features
        const featuresContainer = document.getElementById('product-features');
        featuresContainer.innerHTML = '';
        
        if (product.features && product.features.length > 0) {
          product.features.forEach(feature => {
            const li = document.createElement('li');
            li.textContent = feature;
            featuresContainer.appendChild(li);
          });
        } else {
          featuresContainer.innerHTML = '<li>No features listed.</li>';
        }
        
        // Populate durations
        const durationsContainer = document.getElementById('product-durations');
        durationsContainer.innerHTML = '';
        if (product.durations && product.durations.length > 0) {
          // Create table header
          const table = document.createElement('table');
          table.className = 'min-w-full divide-y divide-gray-200';
          table.innerHTML = `
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration (Days)</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Original Price</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Price</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
            </tbody>
          `;
          
          const tableBody = table.querySelector('tbody');
          const sortedDurations = [...product.durations].sort((a, b) => a.duration_days - b.duration_days);
          
          sortedDurations.forEach(duration => {
            const discountedPrice = duration.original_price * (1 - (duration.discount_percent || 0) / 100);
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            row.innerHTML = `
              <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">${duration.duration_days} days</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">$${duration.original_price.toFixed(2)}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${duration.discount_percent || 0}%</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm font-bold text-green-600">$${discountedPrice.toFixed(2)}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${duration.quantity || 0}</td>
            `;
            tableBody.appendChild(row);
          });
          
          durationsContainer.appendChild(table);
        } else {
          durationsContainer.innerHTML = `
            <div class="p-8 text-center border rounded-lg bg-gray-50">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-sm text-gray-500">No additional durations available.</p>
              <p class="text-xs text-gray-400 mt-1">This product doesn't have any duration packages configured.</p>
            </div>
          `;
        }
        
        // Load and display accounts
        loadProductAccounts(product.id);
      }
      
      // Display product accounts
      function displayProductAccounts(accounts) {
        const accountsList = document.getElementById('accounts-list');
        accountsList.innerHTML = '';
        if (accounts && accounts.length > 0) {
          // Create a table for better display
          const table = document.createElement('table');
          table.className = 'min-w-full divide-y divide-gray-200';
          table.innerHTML = `
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
            </tbody>
          `;
          
          const tableBody = table.querySelector('tbody');
          accounts.forEach(account => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            row.innerHTML = `
              <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">${account.account.id}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${account.account.name || account.account.info || 'N/A'}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 py-1 text-xs font-semibold rounded-full ${
                  account.account.status === 'active' ? 'bg-green-100 text-green-800' : 
                  account.account.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                  'bg-gray-100 text-gray-800'
                }">
                  ${account.account.status || 'Unknown'}
                </span>
              </td>
              <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                <button class="bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-3 rounded" 
                  onclick="showRemoveAccountModal('${account.account.id}')">Remove</button>
              </td>
            `;
            tableBody.appendChild(row);
          });
          
          accountsList.appendChild(table);
        } else {
          accountsList.innerHTML = `
            <div class="p-8 text-center border rounded-lg bg-gray-50">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <p class="text-sm text-gray-500">No accounts assigned to this product yet.</p>
              <p class="text-xs text-gray-400 mt-1">Click "Assign Account" to add accounts to this product.</p>
            </div>
          `;
        }
      }
      
      // Populate account select dropdown
      function populateAccountSelect(accounts) {
        const accountSelect = document.getElementById('accountSelect');
        accountSelect.innerHTML = '<option value="">Select an account</option>';
        
        if (accounts && accounts.length > 0) {
          accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            // Use account name as primary display, fallback to info then ID
            let optionText = account.name || account.info || `Account ${account.id}`;
            if (account.status) {
              optionText += ` (${account.status})`;
            }
            option.textContent = optionText;
            accountSelect.appendChild(option);
          });
        } else {
          const option = document.createElement('option');
          option.disabled = true;
          option.textContent = 'No available accounts to assign';
          accountSelect.appendChild(option);
        }
      }

      // Show assign account modal
      async function showAssignAccountModal() {
        // Show loading state
        const assignBtn = document.getElementById('assignAccountBtn');
        const originalText = assignBtn.textContent;
        assignBtn.textContent = 'Loading...';
        assignBtn.disabled = true;
        
        try {
          // Refresh assignable accounts when modal opens
          const assignableAccounts = await getAssignableAccounts(productId);
          populateAccountSelect(assignableAccounts);
          
          document.getElementById('assignAccountModal').classList.remove('hidden');
        } catch (error) {
          showToast('Failed to load accounts', 'error');
        } finally {
          // Restore button state
          assignBtn.textContent = originalText;
          assignBtn.disabled = false;
        }
      }

      // Hide assign account modal
      function hideAssignAccountModal() {
        document.getElementById('assignAccountModal').classList.add('hidden');
      }

      // Show remove account modal
      function showRemoveAccountModal(accountId) {
        document.getElementById('removeAccountId').value = accountId;
        document.getElementById('removeAccountModal').classList.remove('hidden');
      }

      // Hide remove account modal
      function hideRemoveAccountModal() {
        document.getElementById('removeAccountModal').classList.add('hidden');
      }

      // Assign account to product
      async function assignAccountToProduct(event) {
        event.preventDefault();
        
        const accountId = document.getElementById('accountSelect').value;
        const submitBtn = event.target.querySelector('button[type="submit"]');
        
        if (!accountId) {
          showToast('Please select an account to assign', 'error');
          return;
        }
        
        if (!setupAxios()) return;
        
        // Show loading state
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Assigning...';
        submitBtn.disabled = true;
        
        try {
          await axios.post('/products/assign-accounts', {
            product_id: parseInt(productId),
            account_ids: [parseInt(accountId)]
          });
          
          showToast('Account assigned successfully');
          hideAssignAccountModal();
          
          // Reload accounts
          await loadProductAccounts(productId);
        } catch (error) {
          const message = error.response?.data?.message || 'Failed to assign account. Please try again.';
          showToast(message, 'error');
        } finally {
          // Restore button state
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }
      }
      
      // Remove account from product
      async function removeAccountFromProduct() {
        const accountId = document.getElementById('removeAccountId').value;
        const removeBtn = document.getElementById('confirmRemoveAccountBtn');
        
        if (!setupAxios()) return;
        
        // Show loading state
        const originalText = removeBtn.textContent;
        removeBtn.textContent = 'Removing...';
        removeBtn.disabled = true;
        
        try {
          await axios.delete(`/products/${productId}/accounts/${accountId}`);
          
          showToast('Account removed successfully');
          hideRemoveAccountModal();
          
          // Reload accounts
          await loadProductAccounts(productId);
        } catch (error) {
          const message = error.response?.data?.message || 'Failed to remove account. Please try again.';
          showToast(message, 'error');
        } finally {
          // Restore button state
          removeBtn.textContent = originalText;
          removeBtn.disabled = false;
        }
      }
      
      // Load product accounts
      async function loadProductAccounts(productId) {
        const accounts = await fetchProductAccounts(productId);
        displayProductAccounts(accounts);
        
        // Load additional accounts for assignment
        const assignableAccounts = await getAssignableAccounts(productId);
        populateAccountSelect(assignableAccounts);
      }

      // Delete product
      async function deleteProduct(id) {
        if (!setupAxios()) return;

        try {
          await axios.delete(`/products/${id}`);
          showToast('Product deleted successfully');
          setTimeout(() => {
            window.location.href = '/admin/products';
          }, 1500);
        } catch (error) {
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to delete product. Please try again.';
            showToast(message, 'error');
          }
        }
      }

      // Show delete modal
      function showDeleteModal() {
        document.getElementById('deleteModal').classList.remove('hidden');
      }

      // Hide delete modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
      }

      // Event listeners
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial setup
        setupAxios();
        await checkAuth();

        // Fetch and display product details
        if (productId) {
          const product = await fetchProduct(productId);
          if (product) {
            displayProductDetails(product);
          }
        } else {
          showError('Product ID not found in URL');
        }

        // Button event listeners
        document.getElementById('editBtn').addEventListener('click', () => {
          window.location.href = `/admin/products/${productId}/edit`;
        });

        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/products';
        });

        // Delete modal event listeners
        document.getElementById('deleteBtn').addEventListener('click', showDeleteModal);
        document.getElementById('cancelDeleteBtn').addEventListener('click', hideDeleteModal);
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
          deleteProduct(productId);
        });
        
        // Assign account modal event listeners
        document.getElementById('assignAccountBtn').addEventListener('click', showAssignAccountModal);
        document.getElementById('cancelAssignBtn').addEventListener('click', hideAssignAccountModal);
        document.getElementById('assignAccountForm').addEventListener('submit', assignAccountToProduct);
        
        // Remove account modal event listeners
        document.getElementById('cancelRemoveAccountBtn').addEventListener('click', hideRemoveAccountModal);
        document.getElementById('confirmRemoveAccountBtn').addEventListener('click', removeAccountFromProduct);
        
        // Close modal listeners
        document.getElementById('closeAssignModal').addEventListener('click', hideAssignAccountModal);
      });
    </script>
  </body>
</html>