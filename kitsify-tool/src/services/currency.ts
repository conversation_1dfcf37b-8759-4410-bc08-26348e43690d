import { api } from './api';

export interface USDTConversion {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  formattedAmount: string;
}

export interface USDTRate {
  rate: number;
  description: string;
}

export interface PricingWithDiscountAndCurrencies {
  original: {
    usd: number;
    usdt: USDTConversion;
  };
  discounted: {
    usd: number;
    usdt: USDTConversion;
  };
  discount: {
    amount: number;
    percent: number;
  };
}

class CurrencyService {
  /**
   * Get current USDT exchange rate
   */
  async getUSDTRate(): Promise<USDTRate> {
    try {
      const response = await api.get('/currency/usdt-rate');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching USDT rate:', error);
      throw new Error('Failed to fetch USDT exchange rate');
    }
  }

  /**
   * Convert USD amount to USDT
   */
  async convertUSDToUSDT(amount: number): Promise<USDTConversion> {
    try {
      const response = await api.get(`/currency/convert-to-usdt?amount=${amount}`);
      return response.data.data;
    } catch (error) {
      console.error('Error converting USD to USDT:', error);
      throw new Error('Failed to convert USD to USDT');
    }
  }

  /**
   * Get pricing information with discounts and multiple currencies
   */
  async getPricingWithDiscountAndCurrencies(
    originalPrice: number,
    discountPercent: number = 0,
  ): Promise<PricingWithDiscountAndCurrencies> {
    try {
      const discountedPrice = originalPrice * (1 - discountPercent / 100);
      const discountAmount = originalPrice - discountedPrice;

      const [originalUSDT, discountedUSDT] = await Promise.all([
        this.convertUSDToUSDT(originalPrice),
        this.convertUSDToUSDT(discountedPrice),
      ]);

      return {
        original: {
          usd: originalPrice,
          usdt: originalUSDT,
        },
        discounted: {
          usd: discountedPrice,
          usdt: discountedUSDT,
        },
        discount: {
          amount: discountAmount,
          percent: discountPercent,
        },
      };
    } catch (error) {
      console.error('Error getting pricing with currencies:', error);
      throw new Error('Failed to get pricing information');
    }
  }

  /**
   * Format price with currency
   */
  formatPrice(amount: number, currency: string = 'USD'): string {
    if (currency.toUpperCase() === 'USDT') {
      return `${amount.toFixed(2)} USDT`;
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Get multiple currency conversions for a USD amount
   */
  async getMultiCurrencyConversion(usdAmount: number): Promise<{
    usd: { amount: number; formatted: string };
    usdt: USDTConversion;
  }> {
    try {
      const usdtConversion = await this.convertUSDToUSDT(usdAmount);

      return {
        usd: {
          amount: usdAmount,
          formatted: this.formatPrice(usdAmount, 'USD'),
        },
        usdt: usdtConversion,
      };
    } catch (error) {
      console.error('Error getting multi-currency conversion:', error);
      throw new Error('Failed to get currency conversions');
    }
  }
}

export const currencyService = new CurrencyService();
export default currencyService;
