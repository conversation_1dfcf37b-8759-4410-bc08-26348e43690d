'use client';

import React, { useState, useEffect } from 'react';
import { FaPaypal, FaBitcoin } from 'react-icons/fa';
import CryptomusModal from '@/components/CryptomusModal';

interface PaymentMethod {
  payment_method_id: number;
  payment_method_name: string;
  original_price: number;
  discount_percent: number;
  discounted_price: number;
  savings: number;
  currency: string;
}

interface USDTConversion {
  originalAmount: number;
  convertedAmount: number;
  formattedAmount: string;
  exchangeRate: number;
}

interface PaymentButtonsProps {
  packageId: number;
  durationId: number;
  packageName: string;
  onPayPalClick?: (paymentMethod: PaymentMethod) => void;
  onCryptomusClick?: (paymentMethod: PaymentMethod) => void;
  className?: string;
}

const PaymentButtons: React.FC<PaymentButtonsProps> = ({
  packageId,
  durationId,
  packageName,
  onPayPalClick,
  onCryptomusClick,
  className = '',
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [usdtConversions, setUsdtConversions] = useState<Map<number, USDTConversion>>(new Map());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCryptomusModal, setShowCryptomusModal] = useState(false);
  const [selectedCryptomusMethod, setSelectedCryptomusMethod] = useState<PaymentMethod | null>(null);

  useEffect(() => {
    fetchPaymentMethods();
  }, [packageId, durationId]);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/public/packages/pricing/${packageId}/${durationId}?currency=USD`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      const data = await response.json();
      setPaymentMethods(data.pricing_options || []);

      // Fetch USDT conversions for each payment method
      const conversions = new Map<number, USDTConversion>();
      
      for (const method of data.pricing_options || []) {
        try {
          const usdtResponse = await fetch(
            `/api/currency/convert-to-usdt?amount=${method.discounted_price}`
          );
          
          if (usdtResponse.ok) {
            const usdtData = await usdtResponse.json();
            conversions.set(method.payment_method_id, usdtData.data);
          }
        } catch (usdtError) {
          console.warn(`Failed to get USDT conversion for payment method ${method.payment_method_id}:`, usdtError);
        }
      }

      setUsdtConversions(conversions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handlePayPalClick = (paymentMethod: PaymentMethod) => {
    if (onPayPalClick) {
      onPayPalClick(paymentMethod);
    }
  };

  const handleCryptomusClick = (paymentMethod: PaymentMethod) => {
    setSelectedCryptomusMethod(paymentMethod);
    setShowCryptomusModal(true);
    
    if (onCryptomusClick) {
      onCryptomusClick(paymentMethod);
    }
  };

  const formatPrice = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getPaymentMethodIcon = (methodName: string) => {
    const name = methodName.toLowerCase();
    if (name.includes('paypal')) {
      return <FaPaypal className="mr-2 text-blue-600" />;
    }
    if (name.includes('cryptomus') || name.includes('crypto')) {
      return <FaBitcoin className="mr-2 text-orange-500" />;
    }
    return null;
  };

  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="animate-pulse rounded-lg bg-gray-200 h-12"></div>
        <div className="animate-pulse rounded-lg bg-gray-200 h-12"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center text-red-600 ${className}`}>
        <p>Error loading payment options: {error}</p>
        <button
          onClick={fetchPaymentMethods}
          className="mt-2 text-blue-600 hover:text-blue-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (paymentMethods.length === 0) {
    return (
      <div className={`text-center text-gray-600 ${className}`}>
        <p>No payment methods available</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {paymentMethods.map((method) => {
        const usdtConversion = usdtConversions.get(method.payment_method_id);
        const isPayPal = method.payment_method_name.toLowerCase().includes('paypal');
        const isCryptomus = method.payment_method_name.toLowerCase().includes('cryptomus');

        return (
          <div
            key={method.payment_method_id}
            className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm hover:shadow-md transition-shadow"
          >
            {/* Payment Method Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                {getPaymentMethodIcon(method.payment_method_name)}
                <span className="font-medium text-gray-900 capitalize">
                  {method.payment_method_name}
                </span>
              </div>
              
              {method.discount_percent > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  {method.discount_percent}% OFF
                </span>
              )}
            </div>

            {/* Pricing Information */}
            <div className="space-y-2 mb-4">
              {method.discount_percent > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Original Price:</span>
                  <span className="line-through text-gray-500">
                    {formatPrice(method.original_price)}
                  </span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="font-medium text-gray-900">Final Price (USD):</span>
                <span className="font-bold text-green-600">
                  {formatPrice(method.discounted_price)}
                </span>
              </div>

              {usdtConversion && (
                <div className="flex justify-between">
                  <span className="font-medium text-gray-900">Final Price (USDT):</span>
                  <span className="font-bold text-orange-600">
                    {usdtConversion.formattedAmount}
                  </span>
                </div>
              )}

              {method.savings > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-green-600">You Save:</span>
                  <span className="font-medium text-green-600">
                    {formatPrice(method.savings)}
                  </span>
                </div>
              )}
            </div>

            {/* Payment Button */}
            {isPayPal && (
              <button
                onClick={() => handlePayPalClick(method)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                <FaPaypal className="mr-2" />
                Pay with PayPal
              </button>
            )}

            {isCryptomus && (
              <button
                onClick={() => handleCryptomusClick(method)}
                className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                <FaBitcoin className="mr-2" />
                Pay with Crypto
              </button>
            )}
          </div>
        );
      })}

      {/* Cryptomus Modal */}
      {showCryptomusModal && selectedCryptomusMethod && (
        <CryptomusModal
          isOpen={showCryptomusModal}
          onClose={() => setShowCryptomusModal(false)}
          productName={packageName}
          discountPercent={selectedCryptomusMethod.discount_percent}
          originalPrice={selectedCryptomusMethod.original_price}
          discountedPrice={selectedCryptomusMethod.discounted_price}
        />
      )}
    </div>
  );
};

export default PaymentButtons;
