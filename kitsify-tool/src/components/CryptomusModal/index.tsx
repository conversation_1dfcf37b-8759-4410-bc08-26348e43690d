'use client';

import React from 'react';

type CryptomusModalProps = {
  isOpen: boolean;
  onClose: () => void;
  productName: string;
  discountPercent: number;
  originalPrice: number;
  discountedPrice: number;
};

const CryptomusModal: React.FC<CryptomusModalProps> = ({
  isOpen,
  onClose,
  productName,
  discountPercent,
  originalPrice,
  discountedPrice,
}) => {
  if (!isOpen) {
    return null;
  }

  const handleMessengerRedirect = () => {
    // Use Vietnamese fanpage URL as default (108592857933314)
    const messengerUrl = 'https://www.facebook.com/messages/t/108592857933314';
    window.open(messengerUrl, '_blank', 'noopener,noreferrer');
  };

  const savings = originalPrice - discountedPrice;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">
            🪙 Thanh toán Crypto
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="size-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-6">
          <div className="rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 p-4">
            <h3 className="mb-2 font-semibold text-gray-900">{productName}</h3>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Giá gốc:</span>
                <span className="font-medium text-gray-900">${originalPrice.toFixed(2)}</span>
              </div>

              <div className="flex justify-between text-green-600">
                <span>Giảm giá ({discountPercent}%):</span>
                <span className="font-medium">-${savings.toFixed(2)}</span>
              </div>

              <div className="flex justify-between border-t pt-2 text-lg font-bold">
                <span className="text-gray-900">Giá cuối cùng:</span>
                <span className="text-red-600">${discountedPrice.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-6 rounded-lg bg-blue-50 p-4">
          <p className="text-center text-gray-700">
            💬 Vui lòng liên hệ fanpage để được tư vấn và hỗ trợ thanh toán crypto với giá ưu đãi!
          </p>
        </div>

        <div className="space-y-3">
          <button
            type="button"
            onClick={handleMessengerRedirect}
            className="w-full rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 px-4 py-3 font-medium text-white transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-md"
          >
            💬 Liên hệ Fanpage
          </button>

          <button
            type="button"
            onClick={onClose}
            className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 font-medium text-gray-700 transition-all duration-200 hover:bg-gray-50"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
};

export default CryptomusModal; 